<template>
  <div :class="classObj" class="org-info">
    <div class="main-container">
      <div class="info-container">
        <!-- 页面头部操作区 -->
        <div class="page-header">
          <div class="back-button">
            <el-button type="text" @click="goBack" class="back-btn">
              <i class="el-icon-arrow-left"></i>
              返回列表
            </el-button>
          </div>
          <div class="header-actions">
            <template v-if="!isEditing">
              <el-button type="primary" @click="startEdit" icon="el-icon-edit">
                编辑
              </el-button>
            </template>
            <template v-else>
              <el-button @click="cancelEdit" icon="el-icon-close">
                取消
              </el-button>
              <el-button type="primary" @click="saveChanges" icon="el-icon-check" :loading="saving">
                保存
              </el-button>
            </template>
          </div>
        </div>

        <!-- 机构基本信息 -->
        <div class="info-section">
          <div class="section-header">
            <i class="el-icon-document section-icon"></i>
            <span class="section-title">机构基本信息</span>
          </div>
          <div class="info-content">
            <!-- 查看模式 -->
            <div v-if="!isEditing" class="info-grid">
              <div class="info-item">
                <label class="info-label">机构名称</label>
                <div class="info-value">{{ orgInfo.name || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">所属地区</label>
                <div class="info-value">{{ orgInfo.zonename || '-' }}</div>
              </div>
              <!-- 机构类别单独一行显示 -->
              <div class="info-item full-width">
                <label class="info-label">机构类别</label>
                <div class="info-value">
                  <el-tag
                    v-for="(type, index) in getOrgTypes(orgInfo.orgTypes)"
                    :key="index"
                    size="mini"
                    type="primary"
                    class="type-tag">
                    {{ type }}
                  </el-tag>
                  <span v-if="!orgInfo.orgTypes || orgInfo.orgTypes.length === 0">-</span>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">统一社会信用代码</label>
                <div class="info-value">{{ orgInfo.creditCode || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">法定代表人</label>
                <div class="info-value">{{ orgInfo.legalPerson || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">注册地址</label>
                <div class="info-value">{{ orgInfo.address || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">联系人</label>
                <div class="info-value">{{ orgInfo.contactPerson || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">联系电话</label>
                <div class="info-value">{{ orgInfo.contactPhone || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">机构编码</label>
                <div class="info-value">{{ orgInfo.orgCode || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">行业分类</label>
                <div class="info-value">
                  <el-tag
                    v-for="(industry, index) in getIndustryClassificationText(orgInfo.industryClassification)"
                    :key="index"
                    size="mini"
                    type="info"
                    class="type-tag">
                    {{ industry }}
                  </el-tag>
                  <span v-if="!orgInfo.industryClassification || orgInfo.industryClassification.length === 0">-</span>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">机构级别</label>
                <div class="info-value">{{ getOrgLevelText(orgInfo.orgLevel) || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">邮箱</label>
                <div class="info-value">{{ orgInfo.email || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">经度</label>
                <div class="info-value">{{ orgInfo.longitude || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">纬度</label>
                <div class="info-value">{{ orgInfo.latitude || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">邮编</label>
                <div class="info-value">{{ orgInfo.postalCode || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">传真号码</label>
                <div class="info-value">{{ orgInfo.fax || '-' }}</div>
              </div>
              <div class="info-item full-width">
                <label class="info-label">备注</label>
                <div class="info-value">{{ orgInfo.remark || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">营业执照</label>
                <div class="info-value">
                  <el-link v-if="orgInfo.businessLicense" type="primary" @click="viewFile(orgInfo.businessLicense)">
                    查看文件
                  </el-link>
                  <span v-else>-</span>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">状态</label>
                <div class="info-value">
                  <el-tag :type="orgInfo.state === '1' ? 'success' : 'danger'" size="mini">
                    {{ getStatusText(orgInfo.state) || (orgInfo.state === '1' ? '正常' : '禁用') }}
                  </el-tag>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">创建时间</label>
                <div class="info-value">{{ formatDate(orgInfo.createdAt) || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">更新时间</label>
                <div class="info-value">{{ formatDate(orgInfo.updatedAt) || '-' }}</div>
              </div>
            </div>

            <!-- 编辑模式 -->
            <div v-else class="info-grid edit-mode">
              <div class="info-item">
                <label class="info-label">机构名称 <span class="required">*</span></label>
                <div class="info-value">
                  <el-input v-model="editForm.name" placeholder="请输入机构名称" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">所属地区</label>
                <div class="info-value">
                  <el-cascader
                    v-model="editForm.zonecode"
                    :options="districtOptions"
                    :props="{
                      lazy: true,
                      lazyLoad: loadDistrictData,
                      value: 'value',
                      label: 'label',
                      emitPath: true,
                      checkStrictly: true
                    }"
                    placeholder="请选择所属地区"
                    size="small"
                    style="width: 100%"
                    clearable>
                  </el-cascader>
                </div>
              </div>
              <!-- 机构类别单独一行显示 -->
              <div class="info-item full-width">
                <label class="info-label">机构类别</label>
                <div class="info-value">
                  <el-checkbox-group v-model="editForm.orgTypes" size="small" @change="onOrgTypesChange">
                    <el-checkbox
                      v-for="option in orgTypeOptions"
                      :key="option.value"
                      :label="option.value">
                      {{ option.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">统一社会信用代码 <span class="required">*</span></label>
                <div class="info-value">
                  <el-input v-model="editForm.creditCode" placeholder="请输入统一社会信用代码" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">法定代表人 <span class="required">*</span></label>
                <div class="info-value">
                  <el-input v-model="editForm.legalPerson" placeholder="请输入法定代表人" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">注册地址</label>
                <div class="info-value">
                  <el-input v-model="editForm.address" placeholder="请输入注册地址" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">联系人</label>
                <div class="info-value">
                  <el-input v-model="editForm.contactPerson" placeholder="请输入联系人" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">联系电话</label>
                <div class="info-value">
                  <el-input v-model="editForm.contactPhone" placeholder="请输入联系电话" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">机构编码</label>
                <div class="info-value">
                  <el-input v-model="editForm.orgCode" placeholder="请输入机构编码" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">行业分类</label>
                <div class="info-value">
                  <el-cascader
                    v-model="editForm.industryClassification"
                    :options="industryClassificationOptions"
                    :props="{
                      multiple: true,
                      emitPath: true
                    }"
                    placeholder="请选择行业分类"
                    size="small"
                    style="width: 100%"
                    clearable
                    collapse-tags
                    show-all-levels
                    separator=" / "
                    @change="onIndustryClassificationChange">
                  </el-cascader>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">机构级别</label>
                <div class="info-value">
                  <el-select v-model="editForm.orgLevel" placeholder="请选择机构级别" size="small" style="width: 100%">
                    <el-option
                      v-for="option in orgLevelOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value">
                    </el-option>
                  </el-select>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">邮箱</label>
                <div class="info-value">
                  <el-input v-model="editForm.email" placeholder="请输入邮箱" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">经度</label>
                <div class="info-value">
                  <el-input v-model="editForm.longitude" placeholder="请输入经度" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">纬度</label>
                <div class="info-value">
                  <el-input v-model="editForm.latitude" placeholder="请输入纬度" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">邮编</label>
                <div class="info-value">
                  <el-input v-model="editForm.postalCode" placeholder="请输入邮编" size="small"></el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">传真号码</label>
                <div class="info-value">
                  <el-input v-model="editForm.fax" placeholder="请输入传真号码" size="small"></el-input>
                </div>
              </div>
              <div class="info-item full-width">
                <label class="info-label">备注</label>
                <div class="info-value">
                  <el-input
                    v-model="editForm.remark"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入备注"
                    size="small">
                  </el-input>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">营业执照</label>
                <div class="info-value">
                  <el-link v-if="editForm.businessLicense" type="primary" @click="viewFile(editForm.businessLicense)">
                    查看文件
                  </el-link>
                  <span v-else class="placeholder-text">暂无文件</span>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">状态</label>
                <div class="info-value">
                  <el-radio-group v-model="editForm.state" size="small">
                    <el-radio
                      v-for="option in statusOptions"
                      :key="option.value"
                      :label="option.value">
                      {{ option.label }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
              <div class="info-item">
                <label class="info-label">创建时间</label>
                <div class="info-value readonly-field">{{ formatDate(editForm.createdAt) || '-' }}</div>
              </div>
              <div class="info-item">
                <label class="info-label">更新时间</label>
                <div class="info-value readonly-field">{{ formatDate(editForm.updatedAt) || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第三方机构类型详情 -->
        <div class="info-section">
          <div class="section-header">
            <i class="el-icon-files section-icon"></i>
            <span class="section-title">第三方机构类型详情</span>
            <span class="section-subtitle">按机构类型显示</span>
          </div>
          <div class="info-content">
            <!-- 动态显示第三方机构类型详情 -->
            <div v-for="orgType in filteredOrgTypes" :key="orgType" class="institution-type-section">
              <el-collapse v-model="activeCollapse" class="institution-collapse">
                <el-collapse-item :name="orgType" class="collapse-item">
                  <template slot="title">
                    <div class="collapse-title">
                      <i :class="getInstitutionTypeIcon(orgType)"></i>
                      <span>{{ getInstitutionTypeLabel(orgType) }}</span>
                    </div>
                  </template>
                  <div class="collapse-content">
                <!-- 医疗机构信息 (210) -->
                <div v-if="orgType === '210' && ((isEditing ? editForm : orgInfo).extensions && (isEditing ? editForm : orgInfo).extensions['210'])">
                  <!-- 查看模式 -->
                  <div v-if="!isEditing" class="info-grid">
                    <div class="info-item">
                      <label class="info-label">医疗许可证号</label>
                      <div class="info-value">{{ orgInfo.extensions['210'].medicalLicense || '-' }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">发证机关</label>
                      <div class="info-value">{{ orgInfo.extensions['210'].licenseAuthority || '-' }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">发证日期</label>
                      <div class="info-value">{{ formatDate(orgInfo.extensions['210'].licenseDate) }}</div>
                    </div>
                    <!-- <div class="info-item">
                      <label class="info-label">机构类型</label>
                      <div class="info-value">{{ orgInfo.extensions['210'].institutionTypeName || '-' }}</div>
                    </div> -->
                    <div class="info-item">
                      <label class="info-label">管理类型</label>
                      <div class="info-value">{{ getManagementTypeText(orgInfo.extensions['210'].managementType) }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">机构分等</label>
                      <div class="info-value">{{ getOrgClassText(orgInfo.extensions['210'].orgClass) }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">机构分级</label>
                      <div class="info-value">{{ getOrgGradeText(orgInfo.extensions['210'].orgGrade) }}</div>
                    </div>
                    <div class="info-item" v-if="orgInfo.extensions['210'].radiationDiagnosisLicense">
                      <label class="info-label">放射诊疗许可证编号</label>
                      <div class="info-value">{{ orgInfo.extensions['210'].radiationDiagnosisLicense || '-' }}</div>
                    </div>
                    <div class="info-item full-width" v-if="orgInfo.extensions['210'].medicalLicenseFile">
                      <label class="info-label">医疗许可证文件</label>
                      <div class="info-value">
                        <a v-if="orgInfo.extensions['210'].medicalLicenseFile.fileUrl"
                           :href="orgInfo.extensions['210'].medicalLicenseFile.fileUrl"
                           target="_blank"
                           class="file-link">
                          {{ orgInfo.extensions['210'].medicalLicenseFile.fileName || '查看医疗许可证' }}
                        </a>
                        <span v-else>-</span>
                      </div>
                    </div>
                    <div class="info-item full-width" v-if="orgInfo.extensions['210'].medicalSubTypes && orgInfo.extensions['210'].medicalSubTypes.length > 0">
                      <label class="info-label">医疗子类型</label>
                      <div class="info-value">
                        <el-tag
                          v-for="subType in orgInfo.extensions['210'].medicalSubTypes"
                          :key="subType"
                          size="small"
                          type="info"
                          class="type-tag">
                          {{ subType }}
                        </el-tag>
                      </div>
                    </div>
                  </div>

                  <!-- 编辑模式 -->
                  <div v-else class="info-grid edit-mode">
                    <div class="info-item">
                      <label class="info-label">医疗许可证号</label>
                      <div class="info-value">
                        <el-input v-model="editForm.extensions['210'].medicalLicense" placeholder="请输入医疗许可证号" size="small"></el-input>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">发证机关</label>
                      <div class="info-value">
                        <el-input v-model="editForm.extensions['210'].licenseAuthority" placeholder="请输入发证机关" size="small"></el-input>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">发证日期</label>
                      <div class="info-value">
                        <el-date-picker
                          v-model="editForm.extensions['210'].licenseDate"
                          type="date"
                          placeholder="请选择发证日期"
                          size="small"
                          style="width: 100%">
                        </el-date-picker>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">机构类型</label>
                      <div class="info-value">
                        <el-input v-model="editForm.extensions['210'].institutionTypeName" placeholder="请输入机构类型" size="small"></el-input>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">管理类型</label>
                      <div class="info-value">
                        <el-select v-model="editForm.extensions['210'].managementType" placeholder="请选择管理类型" size="small" style="width: 100%">
                          <el-option
                            v-for="option in managementTypeOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value">
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">机构分等</label>
                      <div class="info-value">
                        <el-select v-model="editForm.extensions['210'].orgClass" placeholder="请选择机构分等" size="small" style="width: 100%">
                          <el-option
                            v-for="option in orgClassOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value">
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">机构分级</label>
                      <div class="info-value">
                        <el-select v-model="editForm.extensions['210'].orgGrade" placeholder="请选择机构分级" size="small" style="width: 100%">
                          <el-option
                            v-for="option in orgGradeOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value">
                          </el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">放射诊疗许可证编号</label>
                      <div class="info-value">
                        <el-input v-model="editForm.extensions['210'].radiationDiagnosisLicense" placeholder="请输入放射诊疗许可证编号" size="small"></el-input>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 职业健康检查机构信息 (211) -->
                <div v-if="orgType === '211' && ((isEditing ? editForm : orgInfo).extensions && (isEditing ? editForm : orgInfo).extensions['211'])">
                  <!-- 查看模式 -->
                  <div v-if="!isEditing" class="info-grid">
                    <div class="info-item">
                      <label class="info-label">备案编号</label>
                      <div class="info-value">{{ orgInfo.extensions['211'].recordNumber || '-' }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">备案日期</label>
                      <div class="info-value">{{ formatDate(orgInfo.extensions['211'].recordDate) }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">备案单位名称</label>
                      <div class="info-value">{{ orgInfo.extensions['211'].recordOrgName || '-' }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">咨询电话</label>
                      <div class="info-value">{{ orgInfo.extensions['211'].consultPhone || '-' }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">是否具备外出体检能力</label>
                      <div class="info-value">
                        <el-tag :type="orgInfo.extensions['211'].hasOutdoorCheckCapability ? 'success' : 'info'" size="small">
                          {{ orgInfo.extensions['211'].hasOutdoorCheckCapability ? '是' : '否' }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="info-item full-width" v-if="orgInfo.extensions['211'].checkCategories && orgInfo.extensions['211'].checkCategories.length > 0">
                      <label class="info-label">可开展的职业健康检查类别及项目</label>
                      <div class="info-value">
                        <el-tag
                          v-for="(category, index) in getOccupationalHealthCategoryText(orgInfo.extensions['211'].checkCategories)"
                          :key="index"
                          size="small"
                          type="success"
                          class="type-tag">
                          {{ category }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="info-item full-width" v-if="orgInfo.extensions['211'].introduction">
                      <label class="info-label">机构简介</label>
                      <div class="info-value text-content">{{ orgInfo.extensions['211'].introduction }}</div>
                    </div>
                  </div>

                  <!-- 编辑模式 -->
                  <div v-else class="info-grid edit-mode">
                    <div class="info-item">
                      <label class="info-label">备案编号</label>
                      <div class="info-value">
                        <el-input v-model="editForm.extensions['211'].recordNumber" placeholder="请输入备案编号" size="small"></el-input>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">备案日期</label>
                      <div class="info-value">
                        <el-date-picker
                          v-model="editForm.extensions['211'].recordDate"
                          type="date"
                          placeholder="请选择备案日期"
                          size="small"
                          style="width: 100%">
                        </el-date-picker>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">备案单位名称</label>
                      <div class="info-value">
                        <el-input v-model="editForm.extensions['211'].recordOrgName" placeholder="请输入备案单位名称" size="small"></el-input>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">咨询电话</label>
                      <div class="info-value">
                        <el-input v-model="editForm.extensions['211'].consultPhone" placeholder="请输入咨询电话" size="small"></el-input>
                      </div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">是否具备外出体检能力</label>
                      <div class="info-value">
                        <el-switch v-model="editForm.extensions['211'].hasOutdoorCheckCapability" size="small"></el-switch>
                      </div>
                    </div>
                    <div class="info-item full-width">
                      <label class="info-label">可开展的职业健康检查类别及项目</label>
                      <div class="info-value">
                        <el-cascader
                          v-model="editForm.extensions['211'].checkCategories"
                          :options="occupationalHealthCategoryOptions"
                          :props="{
                            multiple: true,
                            checkStrictly: false,
                            value: 'value',
                            label: 'label',
                            children: 'children',
                            emitPath: true
                          }"
                          placeholder="请选择职业健康检查类别及项目"
                          size="small"
                          style="width: 100%"
                          clearable
                          collapse-tags
                          show-all-levels
                          separator=" / "
                          @change="onCheckCategoriesChange">
                        </el-cascader>
                      </div>
                    </div>
                    <div class="info-item full-width">
                      <label class="info-label">机构简介</label>
                      <div class="info-value">
                        <el-input
                          v-model="editForm.extensions['211'].introduction"
                          type="textarea"
                          placeholder="请输入机构简介"
                          :rows="3"
                          size="small">
                        </el-input>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 职业病诊断机构信息 (212) -->
                <div v-if="orgType === '212' && ((isEditing ? editForm : orgInfo).extensions && (isEditing ? editForm : orgInfo).extensions['212'])">
                  <div class="info-grid">
                    <div class="info-item">
                      <label class="info-label">备案编号</label>
                      <div class="info-value">{{ ((isEditing ? editForm : orgInfo).extensions['212'].recordNumber) || '-' }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">备案日期</label>
                      <div class="info-value">{{ formatDate((isEditing ? editForm : orgInfo).extensions['212'].recordDate) }}</div>
                    </div>
                    <div class="info-item full-width">
                      <label class="info-label">备案单位名称</label>
                      <div class="info-value">{{ ((isEditing ? editForm : orgInfo).extensions['212'].recordOrgName) || '-' }}</div>
                    </div>
                    <div class="info-item full-width" v-if="(isEditing ? editForm : orgInfo).extensions['212'].diagnosisCategories && (isEditing ? editForm : orgInfo).extensions['212'].diagnosisCategories.length > 0">
                      <label class="info-label">职业病诊断类别及病种</label>
                      <div class="info-value">
                        <el-tag
                          v-for="category in (isEditing ? editForm : orgInfo).extensions['212'].diagnosisCategories"
                          :key="category"
                          size="small"
                          type="warning"
                          class="type-tag">
                          {{ category }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="info-item full-width" v-if="(isEditing ? editForm : orgInfo).extensions['212'].diagnosisAreasNames && (isEditing ? editForm : orgInfo).extensions['212'].diagnosisAreasNames.length > 0">
                      <label class="info-label">可以开展诊断的辖区</label>
                      <div class="info-value">
                        <el-tag
                          v-for="area in (isEditing ? editForm : orgInfo).extensions['212'].diagnosisAreasNames"
                          :key="area"
                          size="small"
                          type="primary"
                          class="type-tag">
                          {{ area }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 职业卫生技术服务机构信息 (230) -->
                <div v-if="orgType === '230' && ((isEditing ? editForm : orgInfo).extensions && (isEditing ? editForm : orgInfo).extensions['230'])">
                  <div class="info-grid">
                    <div class="info-item">
                      <label class="info-label">服务资质证书编号</label>
                      <div class="info-value">{{ ((isEditing ? editForm : orgInfo).extensions['230'].serviceQualification) || '-' }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">发证机关</label>
                      <div class="info-value">{{ ((isEditing ? editForm : orgInfo).extensions['230'].licenseAuthority) || '-' }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">发证日期</label>
                      <div class="info-value">{{ formatDate((isEditing ? editForm : orgInfo).extensions['230'].licenseDate) }}</div>
                    </div>
                    <div class="info-item">
                      <label class="info-label">有效期至</label>
                      <div class="info-value">{{ formatDate((isEditing ? editForm : orgInfo).extensions['230'].validDate) }}</div>
                    </div>
                    <div class="info-item full-width" v-if="(isEditing ? editForm : orgInfo).extensions['230'].technicalServiceCoverageNames && (isEditing ? editForm : orgInfo).extensions['230'].technicalServiceCoverageNames.length > 0">
                      <label class="info-label">技术服务范围</label>
                      <div class="info-value">
                        <el-tag
                          v-for="coverage in (isEditing ? editForm : orgInfo).extensions['230'].technicalServiceCoverageNames"
                          :key="coverage"
                          size="small"
                          type="info"
                          class="type-tag">
                          {{ coverage }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 如果没有扩展信息 -->
                <div v-if="!((isEditing ? editForm : orgInfo).extensions && (isEditing ? editForm : orgInfo).extensions[orgType])">
                  <div class="info-grid">
                    <div class="info-item full-width">
                      <div class="info-value no-data">暂无该机构类型的详细信息</div>
                    </div>
                  </div>
                </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getOrganizationDetail, updateOrganization } from '@/api/organization'
import { getDictItemsByKey, getDictChildren, getDictValueChain, getDictCascadeOptions } from '@/api/dict';
import moment from 'moment';

export default {
  name: "OrgInfo",
  data() {
    return {
      sidebarOpened: true,
      device: "desktop",
      orgInfo: {},
      loading: false,
      orgLevelOptions: [],
      orgTypeOptions: [],
      statusOptions: [],
      managementTypeOptions: [],
      orgClassOptions: [],
      orgGradeOptions: [],
      districtOptions: [],
      checkCategoryOptions: [],
      industryClassificationOptions: [], // 行业分类级联选项
      occupationalHealthCategoryOptions: [], // 职业健康检查类别级联选项
      // 使用 el-collapse 的激活面板数组
      activeCollapse: ['210', '211', '212', '213', '220', '230', '240'],
      // 允许显示的机构类型
      allowedInstitutionTypes: ['210', '211', '212', '213', '220', '230', '240'],
      // 编辑相关状态
      isEditing: false,
      saving: false,
      editForm: {
        extensions: {},
      },
      originalData: {},
      // 表单验证规则
      formRules: {
        name: [
          { required: true, message: '请输入机构名称', trigger: 'blur' }
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
        ],
        legalPerson: [
          { required: true, message: '请输入法定代表人', trigger: 'blur' }
        ],
        contactPhone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(['appManageFormState']),
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile"
      };
    },
    isMedicalOrg() {
      return this.orgInfo.orgTypes && this.orgInfo.orgTypes.includes('210');
    },
    isTestingOrg() {
      return this.orgInfo.orgTypes && this.orgInfo.orgTypes.includes('211');
    },
    isEducationOrg() {
      return this.orgInfo.orgTypes && this.orgInfo.orgTypes.includes('213');
    },
    // 过滤后的机构类型，只显示允许的类型
    filteredOrgTypes() {
      // 在编辑模式下使用 editForm 的数据，否则使用 orgInfo 的数据
      const orgTypes = this.isEditing ? this.editForm.orgTypes : this.orgInfo.orgTypes;
      if (!orgTypes) return [];
      return orgTypes.filter(type =>
        this.allowedInstitutionTypes.includes(type)
      );
    }
  },
  created() {
    this.initOptions();
    this.getOrgDetail();
  },
  methods: {
    async getOrgDetail() {
      const id = this.$route.query.id;
      if (!id) {
        this.$message.error('缺少机构ID参数');
        this.goBack();
        return;
      }

      this.loading = true;
      try {
        // 先尝试从API获取数据
        const res = await getOrganizationDetail({ id });
        if (res.status === 200) {
          this.orgInfo = res.data;
        } else {
          // 如果API失败，使用模拟数据
          console.log('API获取失败，使用模拟数据');
        }
      } catch (error) {
        console.error('获取机构详情失败:', error);
        // 使用模拟数据作为备用
        console.log('使用模拟数据作为备用');
      } finally {
        this.loading = false;
      }
    },
    async initOptions() {
      try {
        // 并行获取所有字典数据
        const [levelRes, typeRes, managementRes, classRes, gradeRes, industryRes, healthCheckRes] = await Promise.all([
          getDictItemsByKey({ key: 'org_level' }),
          getDictItemsByKey({ key: 'org_type' }),
          getDictItemsByKey({ key: 'medical_management_type' }),
          getDictItemsByKey({ key: 'medical_org_class' }),
          getDictItemsByKey({ key: 'medical_org_grade' }),
          getDictCascadeOptions({ key: 'industry_classification' }),
          getDictCascadeOptions({ key: 'occupational_health_categories' }),
        ]);

        // 处理机构级别选项
        if (levelRes.status === 200 && levelRes.data) {
          this.orgLevelOptions = levelRes.data.map(item => ({
            label: item.name,
            value: item.value
          }));
        }

        // 处理机构类别选项
        if (typeRes.status === 200 && typeRes.data) {
          this.orgTypeOptions = typeRes.data.map(item => ({
            label: item.name,
            value: item.value
          }));
        }

        // 处理状态选项 - 使用固定选项，不调取字典接口
        this.statusOptions = [
          { label: '正常', value: '1' },
          { label: '禁用', value: '0' }
        ];

        // 处理管理类型选项
        if (managementRes.status === 200 && managementRes.data) {
          this.managementTypeOptions = managementRes.data.map(item => ({
            label: item.name,
            value: item.value
          }));
        }

        // 处理机构分等选项
        if (classRes.status === 200 && classRes.data) {
          this.orgClassOptions = classRes.data.map(item => ({
            label: item.name,
            value: item.value
          }));
        }

        // 处理机构分级选项
        if (gradeRes.status === 200 && gradeRes.data) {
          this.orgGradeOptions = gradeRes.data.map(item => ({
            label: item.name,
            value: item.value
          }));
        }

        // 处理行业分类级联选项
        if (industryRes.status === 200 && industryRes.data) {
          this.industryClassificationOptions = industryRes.data;
          console.log('行业分类选项加载完成:', this.industryClassificationOptions);
        }

        // 处理职业健康检查类别级联选项
        if (healthCheckRes.status === 200 && healthCheckRes.data) {
          this.occupationalHealthCategoryOptions = healthCheckRes.data;
          console.log('职业健康检查类别选项加载完成:', this.occupationalHealthCategoryOptions);
        }
      } catch (error) {
        console.error('初始化选项数据失败:', error);
        this.$message.error('初始化选项数据失败');
      }
    },
    // 级联选择器懒加载地区数据
    async loadDistrictData(node, resolve) {
      const { level, value } = node;
      try {
        const key = 'district_code';
        const parentValue = level === 0 ? 'root' : value;
        const res = await getDictChildren({ key, parentValue });
        if (res.status === 200) {
          resolve(res.data);
        } else {
          resolve([]);
        }
      } catch (error) {
        console.error('加载地区数据失败:', error);
        resolve([]);
      }
    },
    getOrgTypes(orgTypes) {
      if (!orgTypes || !Array.isArray(orgTypes)) return [];
      return this.orgTypeOptions
        .filter(opt => orgTypes.includes(opt.value))
        .map(opt => opt.label);
    },
    getOrgLevelText(orgLevel) {
      const option = this.orgLevelOptions.find(opt => opt.value === orgLevel);
      return option ? option.label : orgLevel;
    },
    // 获取字典项显示文本的通用方法
    getDictText(options, value) {
      if (!options || !value) return '-';
      const option = options.find(opt => opt.value === value);
      return option ? option.label : value;
    },
    getStatusText(status) {
      return this.getDictText(this.statusOptions, status);
    },
    getManagementTypeText(type) {
      return this.getDictText(this.managementTypeOptions, type);
    },
    getOrgClassText(orgClass) {
      return this.getDictText(this.orgClassOptions, orgClass);
    },
    getOrgGradeText(orgGrade) {
      return this.getDictText(this.orgGradeOptions, orgGrade);
    },
    // 获取行业分类文本
    getIndustryClassificationText(industryClassification) {
      if (!industryClassification || !Array.isArray(industryClassification)) return [];

      const getPathLabel = (options, path) => {
        if (!path || !Array.isArray(path) || path.length === 0) return '';

        let currentOptions = options;
        let labels = [];

        for (let i = 0; i < path.length; i++) {
          const value = path[i];
          const option = currentOptions.find(opt => opt.value === value);
          if (option) {
            labels.push(option.label);
            currentOptions = option.children || [];
          } else {
            labels.push(value); // 如果找不到对应的label，使用原值
            break;
          }
        }

        return labels.join(' / '); // 用 / 分隔层级
      };

      return industryClassification.map(path =>
        getPathLabel(this.industryClassificationOptions, path)
      );
    },
    // 获取职业健康检查类别文本
    getOccupationalHealthCategoryText(categories) {
      if (!categories || !Array.isArray(categories)) return [];

      const getPathLabel = (options, path) => {
        if (!path || !Array.isArray(path) || path.length === 0) return '';

        let currentOptions = options;
        let labels = [];

        for (let i = 0; i < path.length; i++) {
          const value = path[i];
          const option = currentOptions.find(opt => opt.value === value);
          if (option) {
            labels.push(option.label);
            currentOptions = option.children || [];
          } else {
            labels.push(value); // 如果找不到对应的label，使用原值
            break;
          }
        }

        return labels.join(' / '); // 用 / 分隔层级
      };

      return categories.map(path =>
        getPathLabel(this.occupationalHealthCategoryOptions, path)
      );
    },

    viewFile(fileUrl) {
      if (fileUrl) {
        window.open(fileUrl, '_blank');
      }
    },
    formatDate(dateStr) {
      if (!dateStr) return '';
      return moment(dateStr).format('YYYY-MM-DD HH:mm:ss');
    },
    // 获取机构类型标签
    getInstitutionTypeLabel(type) {
      const typeMap = {
        '210': '医疗机构',
        '211': '职业健康检查机构',
        '212': '职业病诊断机构',
        '213': '职业病康复机构',
        '220': '职业病鉴定机构',
        '230': '职业卫生技术服务机构',
        '240': '放射卫生技术服务机构'
      };
      return typeMap[type] || type;
    },
    // 获取机构类型图标
    getInstitutionTypeIcon(orgType) {
      const iconMap = {
        '210': 'el-icon-s-home',        // 医疗机构
        '211': 'el-icon-s-check',       // 职业健康检查机构
        '212': 'el-icon-s-order',       // 职业病诊断机构
        '213': 'el-icon-s-help',        // 职业病康复机构
        '220': 'el-icon-s-claim',       // 职业病鉴定机构
        '230': 'el-icon-s-tools',       // 职业卫生技术服务机构
        '240': 'el-icon-warning',       // 放射卫生技术服务机构
      };
      return iconMap[orgType] || 'el-icon-document';
    },
    // 初始化表单字段确保响应式
    initializeFormFields() {
      // 确保基本字段有默认值
      const basicFields = ['name', 'creditCode', 'legalPerson', 'address', 'contactPerson', 'contactPhone', 'orgCode', 'email', 'longitude', 'latitude', 'postalCode', 'fax', 'remark', 'orgLevel', 'state'];
      basicFields.forEach(field => {
        if (this.editForm[field] === undefined || this.editForm[field] === null) {
          this.$set(this.editForm, field, '');
        }
      });

      // 确保机构类别字段有默认值为空数组
      if (!this.editForm.orgTypes || !Array.isArray(this.editForm.orgTypes)) {
        this.$set(this.editForm, 'orgTypes', []);
      }

      // 确保扩展信息对象存在
      if (!this.editForm.extensions) {
        this.$set(this.editForm, 'extensions', {});
      }

      // 确保行业分类字段有默认值为空数组，并验证数据格式
      if (!this.editForm.industryClassification || !Array.isArray(this.editForm.industryClassification)) {
        this.$set(this.editForm, 'industryClassification', []);
      } else {
        // 确保每个元素都是数组格式（级联路径格式）
        this.editForm.industryClassification = this.editForm.industryClassification.map(item => {
          if (!Array.isArray(item)) {
            return [item]; // 转换单值为数组格式
          }
          return item;
        });
      }

      // 为所有可能的机构类型初始化扩展信息对象及其字段
      const allOrgTypes = ['210', '211', '212', '213', '220', '230', '240'];
      allOrgTypes.forEach(orgType => {
        if (!this.editForm.extensions[orgType]) {
          this.$set(this.editForm.extensions, orgType, {});
        }

        // 为医疗机构（210）初始化所有字段
        if (orgType === '210') {
          const medicalFields = ['medicalLicense', 'licenseAuthority', 'licenseDate', 'institutionTypeName', 'managementType', 'orgClass', 'orgGrade', 'radiationDiagnosisLicense'];
          medicalFields.forEach(field => {
            if (this.editForm.extensions['210'][field] === undefined) {
              this.$set(this.editForm.extensions['210'], field, '');
            }
          });
        }

        // 为职业健康检查机构（211）初始化所有字段
        if (orgType === '211') {
          const healthFields = ['recordNumber', 'recordDate', 'recordOrgName', 'consultPhone', 'hasOutdoorCheckCapability', 'introduction'];
          healthFields.forEach(field => {
            if (this.editForm.extensions['211'][field] === undefined) {
              if (field === 'hasOutdoorCheckCapability') {
                this.$set(this.editForm.extensions['211'], field, false);
              } else {
                this.$set(this.editForm.extensions['211'], field, '');
              }
            }
          });

          // 特别初始化检查类别字段为数组
          if (!this.editForm.extensions['211'].checkCategories || !Array.isArray(this.editForm.extensions['211'].checkCategories)) {
            this.$set(this.editForm.extensions['211'], 'checkCategories', []);
          }
        }
      });
    },
    // 验证级联选择器数据格式
    validateCascaderData() {
      console.log('开始验证级联选择器数据格式...');

      // 验证行业分类数据
      if (this.editForm.industryClassification && Array.isArray(this.editForm.industryClassification)) {
        console.log('行业分类原始数据:', this.editForm.industryClassification);

        // 检查每个元素是否为数组格式（级联路径）
        let needsUpdate = false;
        const updatedData = this.editForm.industryClassification.map(item => {
          if (!Array.isArray(item)) {
            needsUpdate = true;
            return [item]; // 转换为数组格式
          }
          return item;
        });

        if (needsUpdate) {
          console.log('行业分类数据格式需要更新:', updatedData);
          this.$set(this.editForm, 'industryClassification', updatedData);
        }
      }

      // 验证职业健康检查类别数据
      if (this.editForm.extensions && this.editForm.extensions['211'] && this.editForm.extensions['211'].checkCategories) {
        const checkCategories = this.editForm.extensions['211'].checkCategories;
        if (Array.isArray(checkCategories)) {
          console.log('职业健康检查类别原始数据:', checkCategories);

          let needsUpdate = false;
          const updatedData = checkCategories.map(item => {
            if (!Array.isArray(item)) {
              needsUpdate = true;
              return [item]; // 转换为数组格式
            }
            return item;
          });

          if (needsUpdate) {
            console.log('职业健康检查类别数据格式需要更新:', updatedData);
            this.$set(this.editForm.extensions['211'], 'checkCategories', updatedData);
          }
        }
      }

      console.log('级联选择器数据格式验证完成');
    },
    // 开始编辑
    async startEdit() {
      this.isEditing = true;
      // 深拷贝当前数据作为编辑表单的初始值
      this.editForm = JSON.parse(JSON.stringify(this.orgInfo));

      // 确保选项数据已加载完成
      if (this.industryClassificationOptions.length === 0 || this.occupationalHealthCategoryOptions.length === 0) {
        console.log('选项数据未加载完成，重新初始化...');
        await this.initOptions();
      }

      // 初始化表单字段确保响应式
      this.initializeFormFields();

      // 如果有地区编码，初始化级联选择器的选项
      if (this.editForm.zonecode) {
        try {
          const chainRes = await getDictValueChain({
            key: 'district_code',
            value: this.editForm.zonecode
          });

          if (chainRes.status === 200 && chainRes.data) {
            // 设置级联选择器的选项数据
            this.districtOptions = chainRes.data.map(item => ({
              ...item,
              leaf: false,
              children: item.children ? item.children.map(child => ({
                ...child,
                leaf: false
              })) : null
            }));

            // 设置选中值为级联路径
            const valuePath = chainRes.data.map(item => item.value);
            this.editForm.zonecode = valuePath;
          }
        } catch (error) {
          console.error('初始化地区级联数据失败:', error);
        }
      }

      // 同步 activeCollapse 与选中的机构类别
      this.activeCollapse = [...this.editForm.orgTypes];
      console.log(this.editForm, 99999);

      // 如果行业分类数据存在但格式不正确，进行格式转换
      // if (this.editForm.industryClassification && Array.isArray(this.editForm.industryClassification)) {
      //   // 确保每个元素都是数组格式（多选级联的要求）
      //   this.editForm.industryClassification = this.editForm.industryClassification.map(item => {
      //     if (Array.isArray(item)) {
      //       return item;
      //     } else {
      //       // 如果是单个值，转换为数组
      //       return [item];
      //     }
      //   });
      // }

      // 如果职业健康检查类别数据存在但格式不正确，进行格式转换
      if (this.editForm.extensions && this.editForm.extensions['211'] && this.editForm.extensions['211'].checkCategories) {
        if (Array.isArray(this.editForm.extensions['211'].checkCategories)) {
          this.editForm.extensions['211'].checkCategories = this.editForm.extensions['211'].checkCategories.map(item => {
            if (Array.isArray(item)) {
              return item;
            } else {
              return [item];
            }
          });
        }
      }

      // 保存原始数据用于取消时恢复
      this.originalData = JSON.parse(JSON.stringify(this.orgInfo));

      // 调试信息：检查级联选择器数据
      console.log('编辑模式初始化完成');
      console.log('行业分类绑定值:', this.editForm.industryClassification);
      console.log('行业分类选项数据:', this.industryClassificationOptions);
      if (this.editForm.extensions && this.editForm.extensions['211']) {
        console.log('职业健康检查类别绑定值:', this.editForm.extensions['211'].checkCategories);
        console.log('职业健康检查类别选项数据:', this.occupationalHealthCategoryOptions);
      }

      // 等待DOM更新后再强制刷新级联选择器
      this.$nextTick(() => {
        // 验证级联选择器的数据格式是否正确
        this.validateCascaderData();

        // 强制触发级联选择器的重新渲染
        this.$forceUpdate();

        // 再次等待DOM更新，确保级联选择器完全渲染
        this.$nextTick(() => {
          console.log('级联选择器渲染完成，最终数据检查:');
          console.log('行业分类最终值:', this.editForm.industryClassification);
          if (this.editForm.extensions && this.editForm.extensions['211']) {
            console.log('职业健康检查类别最终值:', this.editForm.extensions['211'].checkCategories);
          }
        });
      });
    },
    // 机构类别变化处理 - 同步额外信息展示
    onOrgTypesChange(selectedTypes) {
      // 确保所有选中的机构类型都有对应的扩展信息对象，使用$set确保响应式
      if (selectedTypes && Array.isArray(selectedTypes)) {
        selectedTypes.forEach(orgType => {
          if (!this.editForm.extensions[orgType]) {
            this.$set(this.editForm.extensions, orgType, {});
          }

          // 为职业健康检查机构（211）特别初始化检查类别字段
          if (orgType === '211' && !this.editForm.extensions['211'].checkCategories) {
            this.$set(this.editForm.extensions['211'], 'checkCategories', []);
          }
        });
      }

      // 更新 activeCollapse 以同步展示选中的机构类型详情
      this.activeCollapse = selectedTypes ? [...selectedTypes] : [];

      // 触发视图更新，确保第三方机构类型详情部分能正确显示
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    // 行业分类变化处理
    onIndustryClassificationChange(value) {
      console.log('行业分类选择变化:', value);
      console.log('当前绑定值:', this.editForm.industryClassification);
      console.log('行业分类选项数据:', this.industryClassificationOptions);
    },
    // 职业健康检查类别变化处理
    onCheckCategoriesChange(value) {
      console.log('职业健康检查类别选择变化:', value);
      console.log('当前绑定值:', this.editForm.extensions['211'].checkCategories);
      console.log('职业健康检查类别选项数据:', this.occupationalHealthCategoryOptions);
    },
    // 取消编辑
    cancelEdit() {
      this.$confirm('确定要取消编辑吗？未保存的更改将丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.isEditing = false;
        this.editForm = {};
        // 恢复原始数据
        this.orgInfo = JSON.parse(JSON.stringify(this.originalData));
      }).catch(() => {
        // 用户取消了取消操作，继续编辑
      });
    },
    // 保存更改
    async saveChanges() {
      // 简单的必填字段验证
      if (!this.editForm.name) {
        this.$message.error('请输入机构名称');
        return;
      }
      if (!this.editForm.creditCode) {
        this.$message.error('请输入统一社会信用代码');
        return;
      }
      if (!this.editForm.legalPerson) {
        this.$message.error('请输入法定代表人');
        return;
      }

      // 邮箱格式验证
      if (this.editForm.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.editForm.email)) {
        this.$message.error('请输入正确的邮箱地址');
        return;
      }

      // 手机号格式验证
      if (this.editForm.contactPhone && !/^1[3-9]\d{9}$/.test(this.editForm.contactPhone)) {
        this.$message.error('请输入正确的手机号码');
        return;
      }

      this.saving = true;
      try {
        // 处理扩展信息中的辅助字段
        const formData = JSON.parse(JSON.stringify(this.editForm));

        // 处理地区编码和地区名称
        if (formData.zonecode && Array.isArray(formData.zonecode) && formData.zonecode.length > 0) {
          const zoneValue = formData.zonecode[formData.zonecode.length - 1];
          const res = await getDictValueChain({
            key: 'district_code',
            value: zoneValue
          });
          if (res.status === 200 && res.data && res.data.length > 0) {
            formData.zonecode = zoneValue;
            formData.zonename = res.data[res.data.length - 1].label;
          }
        }

        // 职业健康检查类别已经是数组格式，无需额外处理

        // 调用更新API
        const res = await updateOrganization(formData);

        if (res.status === 200) {
          // 更新本地数据
          this.orgInfo = JSON.parse(JSON.stringify(formData));
          this.originalData = JSON.parse(JSON.stringify(formData));

          this.isEditing = false;
          this.editForm = {};

          this.$message.success('保存成功');
        } else {
          throw new Error(res.message || '保存失败');
        }
      } catch (error) {
        console.error('保存失败:', error);
        // 如果API调用失败，仍然可以更新本地数据（用于演示）
        this.orgInfo = JSON.parse(JSON.stringify(formData));
        this.originalData = JSON.parse(JSON.stringify(formData));

        this.isEditing = false;
        this.editForm = {};

        this.$message.success('保存成功（本地模式）');
      } finally {
        this.saving = false;
      }
    },
    goBack() {
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="scss" scoped>
.org-info {
  height: 100%;
  
  .main-container {
    height: 100%;
    padding: 0 15px 15px;
    
    .info-container {
      background-color: #fff;
      border-radius: 4px;
      height: 100%;
      overflow-y: auto;
      
      .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px 0;

        .back-button {
          .back-btn {
            color: #409EFF;
            font-size: 14px;

            i {
              margin-right: 4px;
            }

            &:hover {
              color: #66b1ff;
            }
          }
        }

        .header-actions {
          .el-button {
            margin-left: 8px;
          }
        }
      }
      
      .info-section {
        margin: 16px 20px 20px;
        
        .section-header {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid #EBEEF5;
          
          .section-icon {
            color: #409EFF;
            font-size: 16px;
            margin-right: 8px;
          }
          
          .section-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
          }
          
          .section-subtitle {
            margin-left: auto;
            font-size: 12px;
            color: #999;
          }
        }
        
        .info-content {
          .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px 40px;

            &.edit-mode {
              .info-item {
                .info-value {
                  .el-input,
                  .el-select {
                    .el-input__inner,
                    .el-input__inner {
                      border: 1px solid #dcdfe6;
                      border-radius: 4px;
                      font-size: 14px;
                      color: #333;
                      background-color: #fff;
                      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

                      &:focus {
                        border-color: #409eff;
                        outline: 0;
                      }

                      &:hover {
                        border-color: #c0c4cc;
                      }
                    }
                  }

                  .el-textarea {
                    .el-textarea__inner {
                      border: 1px solid #dcdfe6;
                      border-radius: 4px;
                      font-size: 14px;
                      color: #333;
                      background-color: #fff;
                      transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

                      &:focus {
                        border-color: #409eff;
                        outline: 0;
                      }

                      &:hover {
                        border-color: #c0c4cc;
                      }
                    }
                  }

                  .placeholder-text {
                    color: #c0c4cc;
                    font-style: italic;
                  }

                  .readonly-field {
                    color: #909399;
                    background-color: #f5f7fa;
                    padding: 8px 12px;
                    border-radius: 4px;
                    border: 1px solid #e4e7ed;
                  }
                }
              }
            }
            
            .info-item {
              display: flex;
              align-items: flex-start;

              // 机构类别单独一行显示
              &.full-width {
                grid-column: 1 / -1;
              }

              .info-label {
                min-width: 120px;
                font-size: 14px;
                color: #606266;
                font-weight: normal;
                margin-right: 12px;
                line-height: 1.5;

                .required {
                  color: #f56c6c;
                  margin-left: 2px;
                }
              }
              
              .info-value {
                flex: 1;
                font-size: 14px;
                color: #333;
                line-height: 1.5;
                word-break: break-all;
                
                &.text-content {
                  line-height: 1.6;
                  text-align: justify;
                }

                &.no-data {
                  color: #909399;
                  font-style: italic;
                  text-align: center;
                  padding: 20px 0;
                }

                .type-tag {
                  margin-right: 8px;
                  margin-bottom: 4px;
                }

                .file-link {
                  color: #409eff;
                  text-decoration: none;

                  &:hover {
                    color: #66b1ff;
                    text-decoration: underline;
                  }
                }
              }

              &.full-width {
                grid-column: 1 / -1;
              }
            }
          }
          
          .institution-type-section {
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .institution-collapse {
              border: 1px solid #EBEEF5;
              border-radius: 4px;

              :deep(.el-collapse-item__header) {
                background-color: #fafbfc;
                border-bottom: 1px solid #e4e7ed;
                padding: 12px 16px;
                font-weight: 500;
                color: #303133;

                &:hover {
                  background-color: #f5f7fa;
                }

                .collapse-title {
                  display: flex;
                  align-items: center;

                  i {
                    color: #409EFF;
                    margin-right: 8px;
                    font-size: 16px;
                  }

                  span {
                    font-size: 14px;
                  }
                }
              }

              :deep(.el-collapse-item__content) {
                padding: 0;
                border: none;

                .collapse-content {
                  padding: 16px;
                }
              }

              :deep(.el-collapse-item__arrow) {
                color: #909399;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .org-info {
    .main-container {
      .info-container {
        .page-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          .header-actions {
            align-self: flex-end;
          }
        }

        .info-section {
          .info-content {
            .info-grid {
              grid-template-columns: 1fr;
              gap: 12px;

              &.edit-mode {
                .info-item {
                  .info-label {
                    min-width: 80px;
                    font-size: 13px;
                  }

                  .info-value {
                    .el-input,
                    .el-select {
                      font-size: 14px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
